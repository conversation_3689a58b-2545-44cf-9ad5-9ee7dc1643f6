name: Test Cases Check

on:
  push:
    branches: [ 'main' ]
  pull_request:
    branches: [ '*' ]

permissions:
  contents: read

jobs:
  test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_DB: checkpointing_db
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
        ports: ["5432:5432"]
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      mongodb:
        image: mongo:6
        env:
          MONGO_INITDB_ROOT_USERNAME: admin
          MONGO_INITDB_ROOT_PASSWORD: admin
          MONGO_INITDB_DATABASE: checkpointing_db
        ports: ["27017:27017"]
        options: >-
          --health-cmd "mongosh --eval 'db.runCommand(\"ping\").ok'"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 3
    steps:
    - uses: actions/checkout@v3

    - name: Install the latest version of uv
      uses: astral-sh/setup-uv@d9e0f98d3fc6adb07d1e3d37f3043649ddad06a1 #v6.5.0
      with:
        version: "latest"

    - name: Install dependencies
      run: |
        uv venv --python 3.12
        uv pip install -e ".[dev]"
        uv pip install -e ".[test]"

    - name: Run test cases with coverage
      run: |
        source .venv/bin/activate
        TAVILY_API_KEY=mock-key make coverage

    - name: Generate HTML Coverage Report
      run: |
        source .venv/bin/activate
        python -m coverage html -d coverage_html

    - name: Upload Coverage Report
      uses: actions/upload-artifact@v4
      with:
        name: coverage-report
        path: coverage_html/
        
    - name: Display Coverage Summary
      run: |
        source .venv/bin/activate
        python -m coverage report