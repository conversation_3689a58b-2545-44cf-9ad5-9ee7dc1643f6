name: Lint Check

on:
  push:
    branches: [ 'main' ]
  pull_request:
    branches: [ '*' ]

permissions:
  contents: read

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3

    - name: Install the latest version of uv
      uses: astral-sh/setup-uv@d9e0f98d3fc6adb07d1e3d37f3043649ddad06a1 #v6.5.0
      with:
        version: "latest"

    - name: Install dependencies
      run: |
        uv venv --python 3.12
        uv pip install -e ".[dev]"

    - name: Run linters
      run: |
        source .venv/bin/activate
        make lint

  lint-frontend:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '22'

    - name: Install pnpm
      run: npm install -g pnpm

    - name: Install frontend dependencies
      run: |
        cd web
        pnpm install --frozen-lockfile

    - name: Run frontend linting
      run: |
        cd web
        pnpm lint

    - name: Check TypeScript types
      run: |
        cd web
        pnpm typecheck

    - name: Build frontend
      run: |
        cd web
        pnpm build