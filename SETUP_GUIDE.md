# DeerFlow 配置和启动指南

## 配置概览

本项目已经为您配置好了以下设置：

### AI模型配置
- **API提供商**: SiliconFlow (https://api.siliconflow.cn)
- **模型**: Qwen/Qwen3-8B
- **API密钥**: sk-iopewlrhdjifcztkffeqpkdskyicedpmpwtqsltysfvlpbee
- **搜索引擎**: DuckDuckGo (无需API密钥)

### 端口配置
- **后端API**: http://localhost:8000
- **前端界面**: http://localhost:3366 (已修改为3366端口)

### 默认页面
- 访问根路径会自动跳转到聊天界面
- 原首页内容可通过 `/landing` 访问

## 快速启动

### 1. 安装依赖

```bash
# 确保您在项目根目录
cd deer-flow

# 安装Python依赖 (使用uv管理)
uv sync

# 安装前端依赖
cd web
pnpm install
cd ..
```

### 2. 启动服务

```bash
# 开发模式启动 (推荐)
./bootstrap.sh -d

# 或者分别启动
# 后端: uv run server.py --reload
# 前端: cd web && pnpm dev
```

### 3. 访问应用

启动成功后，打开浏览器访问：
- **主界面**: http://localhost:3366 (直接进入聊天页面)
- **原首页**: http://localhost:3366/landing
- **API文档**: http://localhost:8000/docs

## 配置文件说明

### .env 文件
包含环境变量配置，主要设置：
- 调试模式: `DEBUG=True`
- CORS设置: 允许3366端口访问
- 搜索引擎: DuckDuckGo
- 安全设置: MCP和Python REPL默认关闭

### conf.yaml 文件
包含AI模型配置：
- 基础模型、推理模型、代码模型都使用 Qwen/Qwen3-8B
- 不同任务使用不同的temperature参数优化
- 搜索引擎配置为DuckDuckGo

## 使用说明

1. **聊天功能**: 直接在界面中输入问题，AI会进行深度研究并生成报告
2. **搜索引擎**: 使用DuckDuckGo进行网络搜索，注重隐私保护
3. **多智能体**: 系统会自动调用不同的专业智能体处理不同类型的任务
4. **报告生成**: 支持生成结构化的研究报告、播客音频等

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口占用
   lsof -i :3366
   lsof -i :8000
   
   # 杀死占用进程
   kill -9 <PID>
   ```

2. **依赖安装失败**
   ```bash
   # 更新uv
   curl -LsSf https://astral.sh/uv/install.sh | sh
   
   # 重新安装依赖
   uv sync --reinstall
   ```

3. **API调用失败**
   - 检查API密钥是否正确
   - 确认网络连接正常
   - 查看 `conf.yaml` 中的配置

### 日志查看

```bash
# 查看后端日志
uv run server.py --reload

# 查看前端日志
cd web && pnpm dev
```

## 高级配置

### 修改AI模型
如需更换AI模型，编辑 `conf.yaml` 文件中的模型配置。

### 更换搜索引擎
在 `.env` 文件中修改 `SEARCH_API` 参数：
- `duckduckgo`: DuckDuckGo (当前配置)
- `tavily`: Tavily (需要API密钥)
- `brave_search`: Brave Search (需要API密钥)
- `arxiv`: Arxiv学术搜索

### 启用高级功能
在 `.env` 文件中可以启用：
- `ENABLE_MCP_SERVER_CONFIGURATION=true`: MCP服务器配置
- `ENABLE_PYTHON_REPL=true`: Python代码执行

⚠️ **安全提醒**: 生产环境中启用这些功能前请确保系统安全。

## 支持

如遇到问题，请检查：
1. 所有依赖是否正确安装
2. 配置文件是否正确
3. 网络连接是否正常
4. API密钥是否有效

更多详细信息请参考项目的 `README.md` 和 `docs/` 目录。
